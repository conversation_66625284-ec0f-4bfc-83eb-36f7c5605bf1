{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test", "outputCapture": "direct"}, "exclude": ["apps/notification-service-e2e/**/*", "apps/fcm-service-e2e/**/*", "apps/sms-service-e2e/**/*", "apps/email-service-e2e/**/*", "apps/apns-service-e2e/**/*", "apps/wns-service-e2e/**/*"]}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "e2e-local", "ciTargetName": "e2e-ci", "disableJestRuntime": false}, "include": ["apps/notification-service-e2e/**/*", "apps/fcm-service-e2e/**/*", "apps/sms-service-e2e/**/*", "apps/email-service-e2e/**/*", "apps/apns-service-e2e/**/*", "apps/wns-service-e2e/**/*"]}], "useDaemonProcess": true, "targetDefaults": {"@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "build": {"cache": true, "inputs": ["production", "^production"]}}, "defaultBase": "develop"}