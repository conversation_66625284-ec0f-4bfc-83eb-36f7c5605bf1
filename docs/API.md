# API Documentation

## Base URL

```
http://localhost:3000/api/v1
```

## Database

This API uses **Prisma** as the ORM with **MongoDB** as the database. All data is stored in MongoDB collections with proper indexing for optimal performance.

## Authentication

## Endpoints

### Notifications

#### Send Notification

**POST** `/notifications/send`

Send a notification to one or more recipients across multiple channels.

**Request Body:**

```json
{
  "appCode": "herond_wallet",
  "authorId": "user_admin_001",
  "channels": ["Fcm"],
  "fallbackChannels": ["Sms"],
  "destinations": [
    {
      "userId": "user_12345",
      "deviceTokens": [
        "dgoDITrkQVWe6qT7lgOG9P:APA91bHCHyHGF3GyDU9DqTBs4n6BwZ4QLegL0wW7LETethugvHw9U7XwayMhREqXgqaP7gwPrY8BMs5xlu63riZWbjeWRPekMOFhZ7WYMQtz2USltrFTv_o"
      ]
    },
    {
      "userId": "user_67890",
      "deviceTokens": ["fcm_token_3"]
    }
  ],
  "eventCode": "USER_TRANSFER_ETH",
  "categoryCode": "Transactional",
  "templateCode": "USER_OTP",
  "locale": "en-US",
  "message": {
    "fcm": {
      "subject": "Transaction Alert",
      "content": "Hello {{username}}, you just transferred {{amount}} ETH",
      "android": {
        "priority": "high",
        "notification": {
          "channelId": "transactions",
          "sound": "ping.mp3"
        }
      },
      "apns": {
        "payload": {
          "aps": {
            "badge": 1,
            "sound": "default",
            "interruptionLevel": "time-sensitive"
          }
        }
      }
    },
    "email": {
      "provider": "SesSmtp",
      "subject": "Transaction Alert",
      "content": "<p>Hello Alice, you just transferred 100 ETH</p>",
      "attachments": [
        {
          "filename": "receipt.pdf",
          "url": "https://cdn.example.com/files/receipt.pdf"
        }
      ]
    },
    "data": {
      "username": "Alice",
      "amount": "100",
      "socketId": "abc123"
    }
  },
  "priority": "High",
  "scheduleTime": "2025-08-20T12:00:00Z"
}
```

**Response:**

```json
{
  "type": "Ok",
  "status": 201,
  "code": null,
  "message": null
}
```

#### Get Notification

**GET** `/notifications/{id}`

Retrieve a specific notification by ID.

**Response:**

```json
{}
```

#### List Notifications

**GET** `/notifications`

Retrieve a paginated list of notifications.

**Query Parameters:**

**Response:**

```json
{}
```

### Templates

#### Create Template

**POST** `/templates`

Create a new notification template.

**Request Body:**

```json
{}
```

#### Get Template

**GET** `/templates/{id}`

Retrieve a specific template by ID.

#### List Templates

**GET** `/templates`

Retrieve a paginated list of templates.

#### Update Template

**PUT** `/templates/{id}`

Update an existing template.

#### Delete Template

**DELETE** `/templates/{id}`

Delete a template.

### Health

#### Health Check

**GET** `/health`

Check the health status of the application and all providers.

**Response:**

```json
{}
```

## Error Responses

All endpoints return errors in the following format:

```json
{}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error
