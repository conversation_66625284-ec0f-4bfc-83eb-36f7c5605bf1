# Provider Service

Each provider service is responsible for sending notifications to a specific channel. The provider service is a standalone NestJS application that can be scaled independently. The provider service is responsible for the following:

- Consuming messages from the message queue
- Sending notifications to the specific channel
- Updating the notification status in the database
- Retrying failed notifications
- Send failed notifications to the dead letter queue

## Message Queue

The provider service consumes messages from the message queue. The message queue is a RabbitMQ topic exchange. The provider service subscribes to the queue based on the channel and priority. For example, the FCM provider service subscribes to the `notifications.fcm.normal` queue. The queue name is constructed as follows:

```
${NOTIFICATION_PREFIX_PATTERN}.${channel.toLowerCase()}.${priority.toLowerCase()}
```

Where `NOTIFICATION_PREFIX_PATTERN` is `notification`, `channel` is the notification channel (e.g. `fcm`, `apns`, `email`, `sms`, `wns`) and `priority` is the notification priority (e.g. `low`, `normal`, `high`, `critical`).

## Configure Provider Service

The provider service is configured using environment variables. The following environment variables are required:

| Variable                  | Description                                                         | Example                                          |
| ------------------------- | ------------------------------------------------------------------- | ------------------------------------------------ |
| `NODE_ENV`                | Environment                                                         | `production`                                     |
| `PORT`                    | Application port                                                    | `3000`                                           |
| `DATABASE_URL`            | MongoDB connection string (Prisma)                                  | `mongodb://localhost:27017/herond-notifications` |
| `RABBITMQ_URL`            | RabbitMQ connection string                                          | `amqp://localhost:5672`                          |
| `NOTIFICATION_PRIORITIES` | Notification priorities to subscribe to the queue (comma separated) | `normal,high,critical`                           |

In the main bootstrap function, the provider service subscribes to the queue based on the channel and priority. The `subscribeQueue` function is used to subscribe to the queue.

```typescript
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { subscribeQueue } from '@libs';
import { AppModule } from './app/app.module';

async function bootstrap() {
  const logger = new Logger('FcmService');

  const app = await NestFactory.create(AppModule);

  // Subscribe to FCM queue
  await subscribeQueue(app, {
    queue: 'notifications.fcm',
    routingKey: 'notification.fcm',
  });
  // Also start HTTP server for health checks
  const port = process.env.PORT || 3001;
  await app.listen(port);

  logger.log(`🚀 Fcm Service is running on: http://localhost:${port}`);
}

bootstrap().catch(error => {
  Logger.error('Failed to start Fcm Service', error);
  process.exit(1);
});
```
