{"name": "notification-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["notification-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/notification-service-e2e/jest.config.js", "passWithNoTests": true}, "dependsOn": [{"projects": "notification-service", "target": "build"}]}}}