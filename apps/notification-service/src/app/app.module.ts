import { CacheModule, DatabaseModule, HostModule, QueueModule, RateLimitingModule } from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from '../infrastructure/auth/auth.module';
import { AppController } from './app.controller';
import { DeviceController } from './controllers/device.controller';
import { HealthController } from './controllers/health.controller';
import { NotificationsController } from './controllers/notifications.controller';
import { UserPreferencesController } from './controllers/user-preferences.controller';
import { DeviceService } from './services/device.service';
import { NotificationService } from './services/notification.service';
import { UserPreferencesService } from './services/user-preferences.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    QueueModule,
    DatabaseModule,
    TerminusModule,
    HostModule,
    AuthModule,
    RateLimitingModule,
    CacheModule,
  ],
  controllers: [
    AppController,
    NotificationsController,
    HealthController,
    DeviceController,
    UserPreferencesController,
  ],
  providers: [NotificationService, DeviceService, UserPreferencesService],
})
export class AppModule {}
