import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IdempotencyInterceptor } from './interceptors/idempotency.interceptor';

@Controller()
export class AppController {
  constructor(private readonly configService: ConfigService) {}

  @Get('/version')
  getData() {
    return {
      NotificationApiVersion: {
        Version: this.configService.get('API_VERSION'),
        BuildTime: this.configService.get('BUILD_TIME'),
      },
    };
  }

  @Get('test')
  @UseInterceptors(IdempotencyInterceptor)
  test() {
    console.log('executing test....');
    return { message: 'Hello World' };
  }
}
