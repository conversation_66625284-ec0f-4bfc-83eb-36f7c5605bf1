import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  type Call<PERSON><PERSON>ler,
  type ExecutionContext,
  type NestInterceptor,
} from '@nestjs/common';
import { catchError, of, tap } from 'rxjs';
import { type Request } from 'express';
import { CacheService } from '@libs';

@Injectable()
export class IdempotencyInterceptor implements NestInterceptor {
  private readonly cacheTtl = 86400;
  constructor(readonly cacheService: CacheService) {}
  async intercept(ctx: ExecutionContext, next: CallHandler) {
    const req = ctx.switchToHttp().getRequest<Request>();

    if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS')
      return next.handle();

    const key = req.header('Idempotency-Key');
    const appCode = req.header('App-Code')?.toLowerCase() ?? 'default';
    if (!key) throw new BadRequestException('Idempotency-Key header is required');

    const reserveKey = `idemp:${appCode}:${key}`;
    const respKey = `${reserveKey}:resp`;

    const cached = await this.cacheService.get(respKey);
    if (cached) return of(JSON.parse(cached));

    const reserved = await this.cacheService.set(reserveKey, 'processing', this.cacheTtl);
    if (!reserved) {
      const maybe = await this.cacheService.get(respKey);
      if (maybe) return of(JSON.parse(maybe));
      throw new HttpException({ status: 'processing' }, HttpStatus.ACCEPTED);
    }

    return next.handle().pipe(
      tap(async resp => {
        await this.cacheService.set(respKey, JSON.stringify(resp), this.cacheTtl);
        await this.cacheService.set(reserveKey, 'done', this.cacheTtl);
      }),
      catchError(async err => {
        await this.cacheService.delete(reserveKey);
        throw err;
      })
    );
  }
}
