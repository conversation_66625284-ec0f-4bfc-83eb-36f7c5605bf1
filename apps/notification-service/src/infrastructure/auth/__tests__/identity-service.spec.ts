// ...existing code...
import { IdentityService } from '../identity.service';
import { ClsService } from 'nestjs-cls';
import { UnauthorizedException } from '@nestjs/common';

describe('IdentityService', () => {
  let identityService: IdentityService;
  let cls: ClsService;

  beforeEach(() => {
    // replaced construction of real ClsService with a lightweight mock
    cls = { get: jest.fn() } as unknown as ClsService;
    identityService = new IdentityService(cls);
  });

  describe('getIdentity', () => {
    it('should throw an error if no identity is set', () => {
      (cls.get as jest.Mock).mockReturnValue(undefined);
      expect(() => identityService.getIdentity()).toThrow(UnauthorizedException);
    });

    it('should return the identity when set', () => {
      const identity = {
        type: 'user',
        userId: 'user-1',
        email: '<EMAIL>',
        scopes: ['read'],
        issuer: 'issuer-a',
      };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getIdentity()).toEqual(identity);
    });
  });

  describe('getUserId', () => {
    it('should return userId for user identity', () => {
      const identity = { type: 'user', userId: 'u-123', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getUserId()).toBe('u-123');
    });

    it('should throw if identity is not user', () => {
      const identity = { type: 'service', clientId: 'c-1', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getUserId()).toThrow(UnauthorizedException);
    });

    it('should throw if user identity missing userId', () => {
      const identity = { type: 'user', scopes: [], issuer: 'iss' } as any;
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getUserId()).toThrow(UnauthorizedException);
    });
  });

  describe('getEmail', () => {
    it('should return email for user identity', () => {
      const identity = { type: 'user', email: '<EMAIL>', userId: 'u-1', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getEmail()).toBe('<EMAIL>');
    });

    it('should throw if identity is not user', () => {
      const identity = { type: 'service', clientId: 'c-1', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getEmail()).toThrow(UnauthorizedException);
    });

    it('should throw if user identity missing email', () => {
      const identity = { type: 'user', userId: 'u-1', scopes: [], issuer: 'iss' } as any;
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getEmail()).toThrow(UnauthorizedException);
    });
  });

  describe('getClientId', () => {
    it('should return clientId for service identity', () => {
      const identity = { type: 'service', clientId: 'svc-1', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getClientId()).toBe('svc-1');
    });

    it('should throw if identity is not service', () => {
      const identity = { type: 'user', userId: 'u-1', scopes: [], issuer: 'iss' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getClientId()).toThrow(UnauthorizedException);
    });

    it('should throw if service identity missing clientId', () => {
      const identity = { type: 'service', scopes: [], issuer: 'iss' } as any;
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(() => identityService.getClientId()).toThrow(UnauthorizedException);
    });
  });

  describe('getScopes', () => {
    it('should return scopes from identity', () => {
      const identity = {
        type: 'user',
        userId: 'u-1',
        email: 'e',
        scopes: ['a', 'b'],
        issuer: 'iss',
      };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getScopes()).toEqual(['a', 'b']);
    });
  });

  describe('getIssuer', () => {
    it('should return issuer from identity', () => {
      const identity = { type: 'service', clientId: 'c-1', scopes: [], issuer: 'issuer-x' };
      (cls.get as jest.Mock).mockReturnValue(identity);
      expect(identityService.getIssuer()).toBe('issuer-x');
    });
  });
});
// ...existing code...
