import { registerAs } from '@nestjs/config';
import type { RedisConfig } from '../../shared';
import { Inject } from '@nestjs/common';
export const redisConfig = registerAs(
  'redis',
  (): RedisConfig => ({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'cache',
  })
);

export const RedisConfigOptions = Symbol('RedisConfig');

export const InjectRedisConfig = () => Inject(RedisConfigOptions);
