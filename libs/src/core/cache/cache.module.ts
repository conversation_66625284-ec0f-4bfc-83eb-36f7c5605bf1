import { Module } from '@nestjs/common';
import { CacheService } from './cache.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisConfig, RedisConfigOptions } from './cache.config';
import type { RedisConfig } from '../../shared';

@Module({
  imports: [ConfigModule.forFeature(redisConfig)],
  providers: [
    CacheService,
    {
      provide: RedisConfigOptions,
      useFactory: (configService: ConfigService) => configService.get<RedisConfig>('redis'),
      inject: [ConfigService],
    },
  ],
  exports: [CacheService],
})
export class CacheModule {}
