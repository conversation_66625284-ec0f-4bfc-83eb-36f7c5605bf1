import { Module } from '@nestjs/common';
import { ThrottlerModule, type ThrottlerOptions } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, APP_FILTER } from '@nestjs/core';
import { RateLimitingStorage } from './rate-limiting.storage';
import { RateLimitingExceptionFilter } from './rate-limiting.exception-filter';
import rateLimitingConfig, { type RateLimitingConfig } from './rate-limiting.config';
import { RateLimitGuard } from './rate-limit.guard';
import { CacheModule, CacheService } from '../cache';

@Module({
  imports: [
    ConfigModule.forFeature(rateLimitingConfig),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule, CacheModule],
      inject: [ConfigService, CacheService],
      useFactory: async (configService: ConfigService, cacheService: CacheService) => {
        const config = configService.get<RateLimitingConfig>('rateLimiting');

        if (!config) {
          throw new Error('Rate limiting configuration not found');
        }

        return {
          throttlers: [
            {
              name: 'default',
              ttl: config.global.ttl * 1000,
              limit: config.global.limit,
            },
          ],
          storage: new RateLimitingStorage(cacheService),
        };
      },
    }),
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: RateLimitGuard,
    },
    {
      provide: APP_FILTER,
      useClass: RateLimitingExceptionFilter,
    },
    RateLimitGuard,
    RateLimitingExceptionFilter,
  ],
  exports: [RateLimitGuard, RateLimitingExceptionFilter],
})
export class RateLimitingModule {
  static register(throttlers: Array<ThrottlerOptions>) {
    return {
      module: RateLimitingModule,
      imports: [
        ConfigModule.forFeature(rateLimitingConfig),
        ThrottlerModule.forRootAsync({
          imports: [ConfigModule, CacheModule],
          inject: [ConfigService, CacheService],
          useFactory: async (configService: ConfigService, cacheService: CacheService) => {
            const config = configService.get<RateLimitingConfig>('rateLimiting');

            if (!config) {
              throw new Error('Rate limiting configuration not found');
            }

            return {
              throttlers: [
                {
                  name: 'default',
                  ttl: config.global.ttl * 1000,
                  limit: config.global.limit,
                },
                ...throttlers,
              ],
              storage: new RateLimitingStorage(cacheService),
            };
          },
        }),
      ],
      providers: [
        {
          provide: APP_GUARD,
          useClass: RateLimitGuard,
        },
        {
          provide: APP_FILTER,
          useClass: RateLimitingExceptionFilter,
        },
        RateLimitGuard,
        RateLimitingExceptionFilter,
      ],
      exports: [RateLimitGuard, RateLimitingExceptionFilter],
    };
  }
}
